2025-06-22 02:06:46,223 - INFO - SPY.py:2109 - Logging configured for Option Trader v3.1.
2025-06-22 02:06:46,223 - INFO - SPY.py:2110 - Log file: c:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v3.1\spy_option_training_v3.1.log
2025-06-22 02:06:46,223 - INFO - SPY.py:2111 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-06-22 02:06:46,239 - INFO - SPY.py:2140 - Initialized shared yfinance session with browser-like headers and very conservative rate limiting.
2025-06-22 02:06:46,239 - INFO - SPY.py:6058 - --- Option Trader v3.1 ---
2025-06-22 02:06:46,239 - INFO - SPY.py:6160 - [INFO] Main: Preparing Data for Training
2025-06-22 02:06:46,239 - INFO - SPY.py:6173 - Fetching train data for SPY (elapsed: 0.0s)
2025-06-22 02:06:50,141 - INFO - SPY.py:2489 - SPY: Keeping columns ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY']
2025-06-22 02:06:50,141 - INFO - SPY.py:2517 - Processed SPY data shape: (1005, 8). Columns: ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY']
2025-06-22 02:06:50,141 - INFO - SPY.py:2518 - Data quality check: 13/8040 (0.2%) zero values
2025-06-22 02:06:50,141 - INFO - SPY.py:731 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1750529206 SUCCESS in 3.90s [Ticker: SPY] [Operation: refactored_fetch]
2025-06-22 02:06:50,141 - INFO - SPY.py:6209 - SUCCESS: Validated authentic training data for SPY: 1005 rows
2025-06-22 02:06:52,148 - INFO - SPY.py:6173 - Fetching train data for ^VIX (elapsed: 5.9s)
2025-06-22 02:06:52,449 - INFO - SPY.py:1951 - Attempt 1/3 with timeout 20s
2025-06-22 02:06:52,996 - INFO - SPY.py:2306 - Processing ^VIX data with shape (1005, 7)
2025-06-22 02:06:52,996 - INFO - SPY.py:2307 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-06-22 02:06:52,996 - INFO - SPY.py:2309 - Raw ^VIX Close values - first: 31.3700008392334, last: 13.279999732971191
2025-06-22 02:06:52,996 - INFO - SPY.py:2229 - Applying VIX validation for VIX
2025-06-22 02:06:52,996 - INFO - SPY.py:2388 - Processed VIX close values - first: 31.3700008392334, last: 13.279999732971191
2025-06-22 02:06:52,996 - INFO - SPY.py:2421 - Market index ^VIX: Keeping only ['close_VIX']
2025-06-22 02:06:52,996 - INFO - SPY.py:2517 - Processed ^VIX data shape: (1005, 1). Columns: ['close_VIX']
2025-06-22 02:06:52,996 - INFO - SPY.py:2518 - Data quality check: 0/1005 (0.0%) zero values
2025-06-22 02:06:52,996 - INFO - SPY.py:1001 - VALIDATION: ^VIX last value: 13.279999732971191
2025-06-22 02:06:52,996 - INFO - SPY.py:731 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1750529212 SUCCESS in 0.85s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-06-22 02:06:52,996 - INFO - SPY.py:6209 - SUCCESS: Validated authentic training data for ^VIX: 1005 rows
2025-06-22 02:06:55,009 - INFO - SPY.py:6173 - Fetching train data for ^VIX3M (elapsed: 8.8s)
2025-06-22 02:06:55,312 - INFO - SPY.py:1951 - Attempt 1/3 with timeout 25s
2025-06-22 02:06:55,994 - INFO - SPY.py:2421 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-06-22 02:06:55,994 - INFO - SPY.py:2517 - Processed ^VIX3M data shape: (1005, 1). Columns: ['close_VIX3M']
2025-06-22 02:06:55,994 - INFO - SPY.py:2518 - Data quality check: 0/1005 (0.0%) zero values
2025-06-22 02:06:55,994 - INFO - SPY.py:1001 - VALIDATION: ^VIX3M last value: 15.40999984741211
2025-06-22 02:06:55,994 - INFO - SPY.py:731 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1750529215 SUCCESS in 0.99s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-06-22 02:06:55,994 - INFO - SPY.py:6209 - SUCCESS: Validated authentic training data for ^VIX3M: 1005 rows
2025-06-22 02:06:58,002 - INFO - SPY.py:6173 - Fetching train data for ^IRX (elapsed: 11.8s)
2025-06-22 02:06:58,304 - INFO - SPY.py:1951 - Attempt 1/3 with timeout 20s
2025-06-22 02:06:58,943 - INFO - SPY.py:2306 - Processing ^IRX data with shape (1005, 7)
2025-06-22 02:06:58,943 - INFO - SPY.py:2307 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-06-22 02:06:58,943 - INFO - SPY.py:2309 - Raw ^IRX Close values - first: 0.14000000059604645, last: 5.224999904632568
2025-06-22 02:06:58,959 - INFO - SPY.py:2246 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-06-22 02:06:58,959 - INFO - SPY.py:2247 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-06-22 02:06:58,959 - INFO - SPY.py:2256 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-06-22 02:06:58,959 - INFO - SPY.py:2259 - IRX (3-month Treasury yield) after conversion to decimal: min=0.000030, max=0.053480, median=0.015600
2025-06-22 02:06:58,959 - INFO - SPY.py:2388 - Processed IRX (3-month Treasury yield) close values - first: 0.0014000000059604645, last: 0.052249999046325685
2025-06-22 02:06:58,959 - INFO - SPY.py:2395 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.052250 (5.2250%)
2025-06-22 02:06:58,959 - INFO - SPY.py:2421 - Market index ^IRX: Keeping only ['close_IRX']
2025-06-22 02:06:58,959 - INFO - SPY.py:2517 - Processed ^IRX data shape: (1005, 1). Columns: ['close_IRX']
2025-06-22 02:06:58,959 - INFO - SPY.py:2518 - Data quality check: 0/1005 (0.0%) zero values
2025-06-22 02:06:58,959 - INFO - SPY.py:1001 - VALIDATION: ^IRX last value: 0.052249999046325685
2025-06-22 02:06:58,959 - INFO - SPY.py:731 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1750529218 SUCCESS in 0.96s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-06-22 02:06:58,959 - INFO - SPY.py:6209 - SUCCESS: Validated authentic training data for ^IRX: 1005 rows
2025-06-22 02:07:00,965 - INFO - SPY.py:6173 - Fetching train data for ^TNX (elapsed: 14.7s)
2025-06-22 02:07:01,268 - INFO - SPY.py:1951 - Attempt 1/3 with timeout 20s
2025-06-22 02:07:02,042 - INFO - SPY.py:2246 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-06-22 02:07:02,042 - INFO - SPY.py:2247 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-06-22 02:07:02,042 - INFO - SPY.py:2256 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-06-22 02:07:02,042 - INFO - SPY.py:2259 - TNX (10-year Treasury yield) after conversion to decimal: min=0.005150, max=0.049880, median=0.029130
2025-06-22 02:07:02,042 - INFO - SPY.py:2388 - Processed TNX (10-year Treasury yield) close values - first: 0.007089999914169312, last: 0.04254000186920166
2025-06-22 02:07:02,042 - INFO - SPY.py:2395 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.042540 (4.2540%)
2025-06-22 02:07:02,042 - INFO - SPY.py:2421 - Market index ^TNX: Keeping only ['close_TNX']
2025-06-22 02:07:02,058 - INFO - SPY.py:2517 - Processed ^TNX data shape: (1005, 1). Columns: ['close_TNX']
2025-06-22 02:07:02,058 - INFO - SPY.py:2518 - Data quality check: 0/1005 (0.0%) zero values
2025-06-22 02:07:02,058 - INFO - SPY.py:1001 - VALIDATION: ^TNX last value: 0.04254000186920166
2025-06-22 02:07:02,058 - INFO - SPY.py:731 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1750529220 SUCCESS in 1.09s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-06-22 02:07:02,058 - INFO - SPY.py:6209 - SUCCESS: Validated authentic training data for ^TNX: 1005 rows
2025-06-22 02:07:02,058 - INFO - SPY.py:6223 - SUCCESS: All training data validated as authentic and complete
2025-06-22 02:07:02,058 - INFO - SPY.py:6226 - Creating combined train features...
2025-06-22 02:07:02,058 - INFO - SPY.py:3120 - Created master index with 1005 unique dates from 2020-06-23 to 2024-06-20
2025-06-22 02:07:02,058 - INFO - SPY.py:3125 - Processing ticker SPY for reindexing: shape=(1005, 8), columns=['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY']
2025-06-22 02:07:02,058 - INFO - SPY.py:3130 - About to reindex SPY with master_index length 1005
2025-06-22 02:07:02,058 - INFO - SPY.py:3020 - Index overlap for SPY: 100.0% (1005/1005 dates)
2025-06-22 02:07:02,058 - INFO - SPY.py:3083 - Successfully reindexed SPY with validation passed
2025-06-22 02:07:02,058 - INFO - SPY.py:3133 - Successfully reindexed SPY to master index. New shape: (1005, 8)
2025-06-22 02:07:02,058 - INFO - SPY.py:3125 - Processing ticker ^VIX for reindexing: shape=(1005, 1), columns=['close_VIX']
2025-06-22 02:07:02,058 - INFO - SPY.py:3130 - About to reindex ^VIX with master_index length 1005
2025-06-22 02:07:02,058 - INFO - SPY.py:3020 - Index overlap for ^VIX: 100.0% (1005/1005 dates)
2025-06-22 02:07:02,058 - INFO - SPY.py:3083 - Successfully reindexed ^VIX with validation passed
2025-06-22 02:07:02,058 - INFO - SPY.py:3133 - Successfully reindexed ^VIX to master index. New shape: (1005, 1)
2025-06-22 02:07:02,058 - INFO - SPY.py:3125 - Processing ticker ^VIX3M for reindexing: shape=(1005, 1), columns=['close_VIX3M']
2025-06-22 02:07:02,058 - INFO - SPY.py:3130 - About to reindex ^VIX3M with master_index length 1005
2025-06-22 02:07:02,058 - INFO - SPY.py:3020 - Index overlap for ^VIX3M: 100.0% (1005/1005 dates)
2025-06-22 02:07:02,073 - INFO - SPY.py:3083 - Successfully reindexed ^VIX3M with validation passed
2025-06-22 02:07:02,073 - INFO - SPY.py:3133 - Successfully reindexed ^VIX3M to master index. New shape: (1005, 1)
2025-06-22 02:07:02,073 - INFO - SPY.py:3125 - Processing ticker ^IRX for reindexing: shape=(1005, 1), columns=['close_IRX']
2025-06-22 02:07:02,073 - INFO - SPY.py:3130 - About to reindex ^IRX with master_index length 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3020 - Index overlap for ^IRX: 100.0% (1005/1005 dates)
2025-06-22 02:07:02,073 - INFO - SPY.py:3083 - Successfully reindexed ^IRX with validation passed
2025-06-22 02:07:02,073 - INFO - SPY.py:3133 - Successfully reindexed ^IRX to master index. New shape: (1005, 1)
2025-06-22 02:07:02,073 - INFO - SPY.py:3125 - Processing ticker ^TNX for reindexing: shape=(1005, 1), columns=['close_TNX']
2025-06-22 02:07:02,073 - INFO - SPY.py:3130 - About to reindex ^TNX with master_index length 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3020 - Index overlap for ^TNX: 100.0% (1005/1005 dates)
2025-06-22 02:07:02,073 - INFO - SPY.py:3083 - Successfully reindexed ^TNX with validation passed
2025-06-22 02:07:02,073 - INFO - SPY.py:3133 - Successfully reindexed ^TNX to master index. New shape: (1005, 1)
2025-06-22 02:07:02,073 - INFO - SPY.py:3151 - Starting combine features with SPY shape: (1005, 8)
2025-06-22 02:07:02,073 - INFO - SPY.py:3152 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-06-22 02:07:02,073 - INFO - SPY.py:3165 - Merging ^VIX (Shape: (1005, 1), Columns: ['close_VIX'])
2025-06-22 02:07:02,073 - INFO - SPY.py:3175 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-06-22 02:07:02,073 - INFO - SPY.py:3210 - SPY index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3211 - ^VIX index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3227 - ^VIX close_VIX sample after join (first 3): [31.3700008392334, 33.84000015258789, 32.220001220703125], last: 13.279999732971191
2025-06-22 02:07:02,073 - INFO - SPY.py:3245 - After joining ^VIX, combined_df columns: ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY', 'close_VIX']
2025-06-22 02:07:02,073 - INFO - SPY.py:3165 - Merging ^VIX3M (Shape: (1005, 1), Columns: ['close_VIX3M'])
2025-06-22 02:07:02,073 - INFO - SPY.py:3175 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-06-22 02:07:02,073 - INFO - SPY.py:3210 - SPY index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3211 - ^VIX3M index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3227 - ^VIX3M close_VIX3M sample after join (first 3): [33.060001373291016, 35.38999938964844, 34.09000015258789], last: 15.40999984741211
2025-06-22 02:07:02,073 - INFO - SPY.py:3245 - After joining ^VIX3M, combined_df columns: ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY', 'close_VIX', 'close_VIX3M']
2025-06-22 02:07:02,073 - INFO - SPY.py:3165 - Merging ^IRX (Shape: (1005, 1), Columns: ['close_IRX'])
2025-06-22 02:07:02,073 - INFO - SPY.py:3175 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-06-22 02:07:02,073 - INFO - SPY.py:3210 - SPY index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3211 - ^IRX index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3227 - ^IRX close_IRX sample after join (first 3): [0.0014000000059604645, 0.0013500000536441803, 0.0012999999523162842], last: 0.052249999046325685
2025-06-22 02:07:02,073 - INFO - SPY.py:3245 - After joining ^IRX, combined_df columns: ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-06-22 02:07:02,073 - INFO - SPY.py:3165 - Merging ^TNX (Shape: (1005, 1), Columns: ['close_TNX'])
2025-06-22 02:07:02,073 - INFO - SPY.py:3175 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-06-22 02:07:02,073 - INFO - SPY.py:3210 - SPY index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3211 - ^TNX index range: 2020-06-23 to 2024-06-20, count: 1005
2025-06-22 02:07:02,073 - INFO - SPY.py:3227 - ^TNX close_TNX sample after join (first 3): [0.007089999914169312, 0.006840000152587891, 0.006740000247955322], last: 0.04254000186920166
2025-06-22 02:07:02,073 - INFO - SPY.py:3245 - After joining ^TNX, combined_df columns: ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-06-22 02:07:02,073 - INFO - SPY.py:3256 - Shape after merging all tickers: (1005, 12)
2025-06-22 02:07:02,073 - INFO - SPY.py:3431 - Created VIX term structure features
2025-06-22 02:07:02,073 - INFO - SPY.py:3467 - Created Treasury yield curve features
2025-06-22 02:07:02,073 - INFO - SPY.py:3323 - Applying normalization to volume_SPY feature...
2025-06-22 02:07:02,073 - INFO - SPY.py:3340 - Volume normalization applied:
2025-06-22 02:07:02,073 - INFO - SPY.py:3341 -   Original volume range: 26457900 to 251783900
2025-06-22 02:07:02,073 - INFO - SPY.py:3342 -   Log-transformed range: 17.0911 to 19.3441
2025-06-22 02:07:02,073 - INFO - SPY.py:3343 -   Normalized range: -3.1197 to 3.5906
2025-06-22 02:07:02,089 - INFO - SPY.py:3344 -   Normalized mean: -0.0000, std: 1.0000
2025-06-22 02:07:02,089 - INFO - SPY.py:3351 - Performing final validation and cleanup...
2025-06-22 02:07:02,089 - INFO - SPY.py:3504 - VIX term structure ratio (vix3m_to_vix_ratio): median=1.1350, Q25=1.0795, Q75=1.1898, outliers=0.2%
2025-06-22 02:07:02,089 - INFO - SPY.py:3504 - Yield curve spread (yield_curve_spread): median=0.0054, Q25=-0.0090, Q75=0.0144, outliers=0.0%
2025-06-22 02:07:02,089 - INFO - SPY.py:3520 - Feature distribution verification completed successfully
2025-06-22 02:07:02,089 - INFO - SPY.py:3386 - Final combined features shape: (1005, 14)
2025-06-22 02:07:02,089 - INFO - SPY.py:3387 - Final columns: ['high_SPY', 'low_SPY', 'volume_SPY', 'atr_SPY', 'rsi_SPY', 'sma_SPY', 'sma50_SPY', 'price_change_3m_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX', 'vix3m_to_vix_ratio', 'yield_curve_spread']
2025-06-22 02:07:02,089 - INFO - SPY.py:6242 - SUCCESS: Training features created and validated: 1005 rows, 14 columns
2025-06-22 02:07:02,089 - INFO - SPY.py:6243 - Creating train environment...
2025-06-22 02:07:02,089 - ERROR - SPY.py:5095 - Missing 'close_SPY'.
2025-06-22 02:07:02,089 - INFO - SPY.py:9258 - [INFO] Main: Script execution completed
2025-06-22 02:07:02,089 - INFO - SPY.py:776 - [INFO] PerformanceMonitor: Performance Summary: 5/5 operations successful (100.0% success rate)
2025-06-22 02:07:02,089 - INFO - SPY.py:784 - [INFO] PerformanceMonitor: Average refactored_fetch time: 1.56s
